<?php
// 用户管理页
session_start();
require_once '../includes/auth.php'; // 引入权限验证文件
require_once '../includes/config.php'; // 引入数据库连接
require_once '../includes/functions.php'; // 引入函数文件

// 确保管理员已登录
requireAdminLogin();

// 处理每页显示数量
$perPageOptions = [10, 30, 100, 1000];
$perPage = isset($_GET['per_page']) && in_array($_GET['per_page'], $perPageOptions) ? (int)$_GET['per_page'] : 10;

// 获取当前页码
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$offset = ($page - 1) * $perPage;

// 处理搜索
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$searchCondition = '';
$params = [];
if (!empty($search)) {
    $searchCondition = "WHERE username LIKE :search OR name LIKE :search OR email LIKE :search";
    $params[':search'] = "%$search%";
}


// 获取总记录数
$countSql = "SELECT COUNT(*) FROM x_users $searchCondition";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalRecords = $countStmt->fetchColumn();
$totalPages = ceil($totalRecords / $perPage);

// 获取用户列表
$sql = "SELECT user_id, username, name, email, phone, status, role, notes, avatar, last_login_at 
        FROM x_users $searchCondition 
        ORDER BY user_id DESC 
        LIMIT :offset, :per_page";
$stmt = $pdo->prepare($sql);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':per_page', $perPage, PDO::PARAM_INT);
if (!empty($params)) {
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
}
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);


// 处理添加用户请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_user') {
    try {
        $avatarPath = '';
        
        // 处理头像上传
        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
            $uploadDir = '../uploads/avatars/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            $fileExtension = pathinfo($_FILES['avatar']['name'], PATHINFO_EXTENSION);
            $fileName = uniqid() . '.' . $fileExtension;
            $uploadFile = $uploadDir . $fileName;
            
            if (move_uploaded_file($_FILES['avatar']['tmp_name'], $uploadFile)) {
                $avatarPath = '../uploads/avatars/' . $fileName;
            }
        }

        // 插入用户数据
        $sql = "INSERT INTO x_users (username, password, name, email, phone, role, status, notes, avatar) 
                VALUES (:username, :password, :name, :email, :phone, :role, :status, :notes, :avatar)";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'username' => $_POST['username'],
            'password' => password_hash($_POST['password'], PASSWORD_DEFAULT),
            'name' => $_POST['name'],
            'email' => $_POST['email'],
            'phone' => $_POST['phone'],
            'role' => $_POST['role'],
            'status' => $_POST['status'],
            'notes' => $_POST['notes'],
            'avatar' => $avatarPath
        ]);

        if ($result) {
            header('Location: manage_users.php?message=用户添加成功');
            exit;
        } else {
            throw new Exception('数据库插入失败');
        }
    } catch (PDOException $e) {
        $error = '添加用户失败：' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="assets/css/source-sans-pro.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/css/font-awesome.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/css/adminlte.min.css">
    
    <style>
        .form-row {
            display: flex;
            flex-wrap: wrap;
        }
        .form-group {
            flex: 1 1 45%; /* 双列显示 */
            margin: 10px;
        }
        #avatarPreview {
            width: 100px;
            height: 100px;
            border: 1px solid #ccc;
            display: none; /* 初始隐藏 */
        }

        /* 状态样式 */
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            white-space: nowrap;
            color: white; /* 文字颜色设为白色 */
            position: relative; /* 设置为相对定位 */
            display: inline-block; /* 让它适应内容 */
        }
        .status-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 0.25rem;
            z-index: -1; /* 使背景层在文字后面 */
        }

    /* 管理员角色样式 */
    .role-admin {
        background-color: #007bff; /* 蓝色背景 */
        color: white; /* 白色文字 */
    }

    /* 销售员角色样式 */
    .role-salesman {
        background-color: #28a745; /* 绿色背景 */
        color: white; /* 白色文字 */
    }

    /* 其他角色样式 */
    .role-other {
        background-color: #ffc107; /* 黄色背景 */
        color: black; /* 黑色文字 */
    }

    /* 头像预览样式 */
    .avatar-preview {
        width: 64px; /* 头像宽度 */
        height: 64px; /* 头像高度 */
        object-fit: cover; /* 保持比例裁剪 */
        border-radius: 50%; /* 圆形头像 */
        border: 2px solid #ddd; /* 边框样式 */
    }
</style>

</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="index.php" class="nav-link">管理后台首页</a>
            </li>
        </ul>

        <ul class="navbar-nav ml-auto">
            <li class="nav-item">
                <a class="nav-link" href="#" role="button">
                    <i class="fas fa-bell"></i>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" role="button">
                    <i class="fas fa-user"></i>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Sidebar Container -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Content Header -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6"><h1>用户管理</h1></div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                            <li class="breadcrumb-item active">用户管理</li>
                        </ol>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                        <!-- Table Tools -->
                                <div class="row">
                            <div class="col-md-6 d-flex gap-2">
                            <!-- 搜索框 -->
                            <form class="search-box col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" placeholder="搜索用户名/姓名/邮箱" 
                                           value="<?php echo htmlspecialchars($search); ?>">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <?php if (!empty($search)): ?>
                                        <a href="?" class="btn btn-outline-secondary" title="清除">
                                            <i class="fas fa-times-circle"></i> 清除
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </form>
                                    </div>
                            <div class="col-md-4 text-right">
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                    <i class="fas fa-plus"></i> 添加用户
                                </button>
                            </div>
                                </div>
                            </div>
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th width="30px">
                                            <input type="checkbox">
                                        </th>
                                        <th>头像</th>
                                        <th>ID</th>
                                        <th>用户名</th>
                                        <th>姓名</th>
                                        <th>邮箱</th>
                                        <th>电话</th>
                                        <th>角色</th>
                                        <th>状态</th>
                                        <th>最后登录</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td><input type="checkbox"></td>
                                            <td>
                                                <?php 
                                                // 首先判断 avatar 是否存在且长度大于等于 4
                                                if (!empty($user['avatar']) && strlen($user['avatar']) >= 4): ?>
                                                    <img src="<?php echo htmlspecialchars($user['avatar']); ?>" class="avatar-img" alt="用户头像">
                                                <?php else: ?>
                                                    <img src="images/avatars/gly_default_avatar.jpg" class="avatar-img" alt="默认头像">
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($user['user_id']); ?></td>
                                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td><?php echo htmlspecialchars($user['name']); ?></td>						
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td><?php echo htmlspecialchars($user['phone']); ?></td>
                                            <td>
                                                <span class="badge <?php echo ($user['role'] === 'admin') ? 'role-admin' : (($user['role'] === 'salesman') ? 'role-salesman' : 'role-other'); ?>">
                                                    <?php echo htmlspecialchars($user['role']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="status-<?php echo $user['status']; ?>">
                                                    <?php
                                                    $statusMap = [
                                                        'active' => '正常',
                                                        'inactive' => '未激活',
                                                        'disabled' => '已禁用'
                                                    ];
                                                    echo $statusMap[$user['status']] ?? $user['status'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo $user['last_login_at'] ? date('Y-m-d H:i', strtotime($user['last_login_at'])) : '从未登录'; ?></td>
                                            <td><?php echo htmlspecialchars($user['notes']); ?></td>
                                            <td>
					    
					    
                                                    <button class="btn btn-sm btn-warning" title="修改" onclick="window.location.href='edit_user.php?id=<?php echo $user['user_id']; ?>'">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($user['user_id'] !== $_SESSION['user_id']): ?>
                                                        <button class="btn btn-sm btn-danger" title="删除" onclick="window.location.href='delete_user.php?id=<?php echo $user['user_id']; ?>'">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-danger" title="删除" disabled>
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
						    

                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                            <div class="card-footer clearfix">
                                <div class="float-left d-flex align-items-center">
                                    <form method="get" class="form-inline">
                                        <select class="form-control form-control-sm" name="per_page" onchange="this.form.submit()" style="width: 100px;">
                                            <option value="10" <?php echo $perPage == 10 ? 'selected' : ''; ?>>10条/页</option>
                                            <option value="30" <?php echo $perPage == 30 ? 'selected' : ''; ?>>30条/页</option>
                                            <option value="100" <?php echo $perPage == 100 ? 'selected' : ''; ?>>100条/页</option>
                                            <option value="1000" <?php echo $perPage == 1000 ? 'selected' : ''; ?>>1000条/页</option>
                                        </select>
                                    </form>
                                </div>
                                <div class="float-right d-flex align-items-center">
                                    <span class="mr-2">共 <?php echo $totalRecords; ?> 条记录</span>
                                    <ul class="pagination pagination-sm m-0 mr-2">
                                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&per_page=<?php echo $perPage; ?>&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>">«</a>
                                        </li>
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>&per_page=<?php echo $perPage; ?>&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&per_page=<?php echo $perPage; ?>&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>">»</a>
                                        </li>
                                    </ul>
                                    <div class="input-group input-group-sm" style="width: 150px;">
                                        <input type="number" class="form-control" id="gotoPage" min="1" placeholder="页码" style="text-align: center;">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" onclick="gotoPage()">
                                                跳转
                                            </button>
                                        </div>
                                    </div>
                                    <script>
                                    function gotoPage() {
                                        const page = document.getElementById('gotoPage').value;
                                        const perPage = <?php echo $perPage; ?>;
                                        if(page > 0 && page <= <?php echo $totalPages; ?>) {
                                            window.location.href = `?page=${page}&per_page=${perPage}&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>`;
                                        }
                                    }
                                    </script>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="float-right d-none d-sm-block">
            <b>Version</b> 1.0.1
        </div>
        <strong>Copyright &copy; 2025 <a href="#">X管家业务系统</a>.</strong> All rights reserved.
    </footer>
</div>
<!-- jQuery -->
<script src="assets/js/jquery-3.7.1.min.js"></script>
<!-- Bootstrap 5.3.0 -->
<script src="assets/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="assets/js/adminlte.min.js"></script>
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="add_user">
                        
                        <!-- 添加头像上传字段 -->
                        <div class="text-center mb-4">
                            <label class="form-label fw-bold d-block">头像</label>
                            <div class="d-flex justify-content-center">
                                <div style="position: relative; cursor: pointer;">
                                    <img id="avatarPreview" src="assets/images/default-avatar.png" 
                                         class="avatar-preview rounded-circle shadow-sm" alt="头像预览" style="width: 100px; height: 100px; object-fit: cover; border: 2px solid #dee2e6;">
                                    <input type="file" id="avatarInput" class="d-none" name="avatar" 
                                           accept="image/*" onchange="previewAvatar(this)">
                                </div>
                            </div>
                        </div>
                        <script>
                            // 点击图片框触发文件选择
                            document.getElementById('avatarPreview').addEventListener('click', function() {
                                document.getElementById('avatarInput').click();
                            });

                            // 图片预览功能
                            function previewAvatar(input) {
                                if (input.files && input.files[0]) {
                                    const reader = new FileReader();
                                    reader.onload = function(e) {
                                        document.getElementById('avatarPreview').src = e.target.result;
                                    };
                                    reader.readAsDataURL(input.files[0]);
                                }
                            }
                        </script>

                        <div class="form-row mb-3">
                            <div class="form-group col-md-6">
                                <label class="form-label fw-bold">用户名</label>
                                <input type="text" class="form-control shadow-sm" name="username" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label class="form-label fw-bold">密码</label>
                                <input type="password" class="form-control shadow-sm" name="password" required>
                            </div>
                        </div>

                        <div class="form-row mb-3">
                            <div class="form-group col-md-6">
                                <label class="form-label fw-bold">姓名</label>
                                <input type="text" class="form-control shadow-sm" name="name">
                            </div>
                            <div class="form-group col-md-6">
                                <label class="form-label fw-bold">邮箱</label>
                                <input type="email" class="form-control shadow-sm" name="email">
                            </div>
                        </div>

                        <div class="form-row mb-3">
                            <div class="form-group col-md-6">
                                <label class="form-label fw-bold">电话</label>
                                <input type="tel" class="form-control shadow-sm" name="phone">
                            </div>
                            <div class="form-group col-md-6">
                                <label class="form-label fw-bold">角色</label>
                                <select class="form-select shadow-sm" name="role" required>
                                    <option value="admin">管理员</option>
                                    <option value="salesman">业务员</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row mb-3">
                            <div class="form-group col-md-6">
                                <label class="form-label fw-bold">状态</label>
                                <select class="form-select shadow-sm" name="status">
                                    <option value="active">正常</option>
                                    <option value="inactive">未激活</option>
                                    <option value="disabled">已禁用</option>
                                </select>
                            </div>
                            <div class="form-group col-md-6">
                                <label class="form-label fw-bold">备注</label>
                                <textarea class="form-control shadow-sm" name="notes" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="addUserForm" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>


    function submitAddUserForm() {
        const form = document.getElementById('addUserForm');
        const formData = new FormData(form);
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
        // 发送 AJAX 请求到当前页面
        fetch('manage_users.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 添加成功
                alert('用户添加成功！');
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                modal.hide();
                // 刷新页面
                window.location.reload();
            } else {
                // 添加失败
                alert('添加用户失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('添加用户时发生错误，请重试。');
        });
    }
    </script>
    
</style>

<!-- 页面脚本 -->
<script>
function changePerPage(value) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('per_page', value);
    urlParams.set('page', 1);
    window.location.href = '?' + urlParams.toString();
}

// 头像预览功能
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('avatarPreview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// 表单提交前验证
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    const username = this.querySelector('[name="username"]').value;
    const password = this.querySelector('[name="password"]').value;
    
    if (!username || !password) {
        e.preventDefault();
        alert('用户名和密码不能为空！');
        return false;
    }
});
</script>
    
</body>
</html>

<style>
.avatar-img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
}
.status-active { color: #28a745; }
.status-inactive { color: #ffc107; }
.status-disabled { color: #dc3545; }
