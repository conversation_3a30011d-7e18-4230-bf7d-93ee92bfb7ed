<?php
// 用户管理页
session_start();
require_once '../includes/auth.php'; // 引入权限验证文件
require_once '../includes/config.php'; // 引入数据库连接
require_once '../includes/functions.php'; // 引入函数文件

// 确保管理员已登录
requireAdminLogin();

// 处理每页显示数量
$perPageOptions = [10, 30, 100, 1000];
$perPage = isset($_GET['per_page']) && in_array($_GET['per_page'], $perPageOptions) ? (int)$_GET['per_page'] : 10;

// 获取当前页码
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$offset = ($page - 1) * $perPage;

// 处理搜索
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$searchCondition = '';
$params = [];
if (!empty($search)) {
    $searchCondition = "WHERE username LIKE :search OR name LIKE :search OR email LIKE :search";
    $params[':search'] = "%$search%";
}


// 获取总记录数
$countSql = "SELECT COUNT(*) FROM x_users $searchCondition";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalRecords = $countStmt->fetchColumn();
$totalPages = ceil($totalRecords / $perPage);

// 获取用户列表
$sql = "SELECT user_id, username, name, email, phone, status, role, notes, avatar, last_login_at 
        FROM x_users $searchCondition 
        ORDER BY user_id DESC 
        LIMIT :offset, :per_page";
$stmt = $pdo->prepare($sql);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':per_page', $perPage, PDO::PARAM_INT);
if (!empty($params)) {
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
}
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);


// 处理添加用户请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_user') {
    try {
        $avatarPath = '';
        
        // 处理头像上传
        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
            $uploadDir = '../uploads/avatars/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            $fileExtension = pathinfo($_FILES['avatar']['name'], PATHINFO_EXTENSION);
            $fileName = uniqid() . '.' . $fileExtension;
            $uploadFile = $uploadDir . $fileName;
            
            if (move_uploaded_file($_FILES['avatar']['tmp_name'], $uploadFile)) {
                $avatarPath = '../uploads/avatars/' . $fileName;
            }
        }

        // 插入用户数据
        $sql = "INSERT INTO x_users (username, password, name, email, phone, role, status, notes, avatar) 
                VALUES (:username, :password, :name, :email, :phone, :role, :status, :notes, :avatar)";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'username' => $_POST['username'],
            'password' => password_hash($_POST['password'], PASSWORD_DEFAULT),
            'name' => $_POST['name'],
            'email' => $_POST['email'],
            'phone' => $_POST['phone'],
            'role' => $_POST['role'],
            'status' => $_POST['status'],
            'notes' => $_POST['notes'],
            'avatar' => $avatarPath
        ]);

        if ($result) {
            header('Location: manage_users.php?message=用户添加成功');
            exit;
        } else {
            throw new Exception('数据库插入失败');
        }
    } catch (PDOException $e) {
        $error = '添加用户失败：' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="assets/css/source-sans-pro.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/css/font-awesome.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/css/adminlte.min.css">
    
    <style>
        /* 表格头像样式 */
        .avatar-img {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 50%;
        }

        /* 状态样式 */
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            white-space: nowrap;
            color: white;
            position: relative;
            display: inline-block;
        }
        .status-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 0.25rem;
            z-index: -1;
        }

        /* 角色样式 */
        .role-admin {
            background-color: #007bff;
            color: white;
        }
        .role-salesman {
            background-color: #28a745;
            color: white;
        }
        .role-other {
            background-color: #ffc107;
            color: black;
        }

        /* 状态颜色 */
        .status-active { color: #28a745; }
        .status-inactive { color: #ffc107; }
        .status-disabled { color: #dc3545; }

        /* 模态框头像上传样式 */
        .avatar-upload-container {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }

        .avatar-upload-container:hover .avatar-overlay {
            display: flex !important;
        }

        .avatar-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100px;
            height: 100px;
            background: rgba(0,0,0,0.6);
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        /* 模态框样式优化 */
        .modal-header.bg-primary {
            border-bottom: none;
        }

        .modal-content {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .card-outline.card-primary {
            border-top: 3px solid #007bff;
        }

        /* 输入框图标样式 */
        .input-group-text {
            background-color: #f8f9fa;
            border-right: none;
        }

        .input-group .form-control {
            border-left: none;
        }

        .input-group .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        /* 必填字段标识 */
        .text-danger {
            color: #dc3545 !important;
        }

        /* 表单标签样式 */
        .font-weight-bold {
            font-weight: 600 !important;
        }
    </style>

</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="index.php" class="nav-link">管理后台首页</a>
            </li>
        </ul>

        <ul class="navbar-nav ml-auto">
            <li class="nav-item">
                <a class="nav-link" href="#" role="button">
                    <i class="fas fa-bell"></i>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" role="button">
                    <i class="fas fa-user"></i>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Sidebar Container -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Content Header -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6"><h1>用户管理</h1></div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                            <li class="breadcrumb-item active">用户管理</li>
                        </ol>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                        <!-- Table Tools -->
                                <div class="row">
                            <div class="col-md-6 d-flex gap-2">
                            <!-- 搜索框 -->
                            <form class="search-box col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" placeholder="搜索用户名/姓名/邮箱" 
                                           value="<?php echo htmlspecialchars($search); ?>">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <?php if (!empty($search)): ?>
                                        <a href="?" class="btn btn-outline-secondary" title="清除">
                                            <i class="fas fa-times-circle"></i> 清除
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </form>
                                    </div>
                            <div class="col-md-4 text-right">
                                <button type="button" class="btn btn-success" data-toggle="modal" data-target="#addUserModal">
                                    <i class="fas fa-plus"></i> 添加用户
                                </button>
                            </div>
                                </div>
                            </div>
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th width="30px">
                                            <input type="checkbox">
                                        </th>
                                        <th>头像</th>
                                        <th>ID</th>
                                        <th>用户名</th>
                                        <th>姓名</th>
                                        <th>邮箱</th>
                                        <th>电话</th>
                                        <th>角色</th>
                                        <th>状态</th>
                                        <th>最后登录</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td><input type="checkbox"></td>
                                            <td>
                                                <?php 
                                                // 首先判断 avatar 是否存在且长度大于等于 4
                                                if (!empty($user['avatar']) && strlen($user['avatar']) >= 4): ?>
                                                    <img src="<?php echo htmlspecialchars($user['avatar']); ?>" class="avatar-img" alt="用户头像">
                                                <?php else: ?>
                                                    <img src="images/avatars/gly_default_avatar.jpg" class="avatar-img" alt="默认头像">
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($user['user_id']); ?></td>
                                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td><?php echo htmlspecialchars($user['name']); ?></td>						
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td><?php echo htmlspecialchars($user['phone']); ?></td>
                                            <td>
                                                <span class="badge <?php echo ($user['role'] === 'admin') ? 'role-admin' : (($user['role'] === 'salesman') ? 'role-salesman' : 'role-other'); ?>">
                                                    <?php echo htmlspecialchars($user['role']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="status-<?php echo $user['status']; ?>">
                                                    <?php
                                                    $statusMap = [
                                                        'active' => '正常',
                                                        'inactive' => '未激活',
                                                        'disabled' => '已禁用'
                                                    ];
                                                    echo $statusMap[$user['status']] ?? $user['status'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo $user['last_login_at'] ? date('Y-m-d H:i', strtotime($user['last_login_at'])) : '从未登录'; ?></td>
                                            <td><?php echo htmlspecialchars($user['notes']); ?></td>
                                            <td>
					    
					    
                                                    <button class="btn btn-sm btn-warning" title="修改" onclick="window.location.href='edit_user.php?id=<?php echo $user['user_id']; ?>'">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($user['user_id'] !== $_SESSION['user_id']): ?>
                                                        <button class="btn btn-sm btn-danger" title="删除" onclick="window.location.href='delete_user.php?id=<?php echo $user['user_id']; ?>'">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-danger" title="删除" disabled>
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
						    

                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                            <div class="card-footer clearfix">
                                <div class="float-left d-flex align-items-center">
                                    <form method="get" class="form-inline">
                                        <select class="form-control form-control-sm" name="per_page" onchange="this.form.submit()" style="width: 100px;">
                                            <option value="10" <?php echo $perPage == 10 ? 'selected' : ''; ?>>10条/页</option>
                                            <option value="30" <?php echo $perPage == 30 ? 'selected' : ''; ?>>30条/页</option>
                                            <option value="100" <?php echo $perPage == 100 ? 'selected' : ''; ?>>100条/页</option>
                                            <option value="1000" <?php echo $perPage == 1000 ? 'selected' : ''; ?>>1000条/页</option>
                                        </select>
                                    </form>
                                </div>
                                <div class="float-right d-flex align-items-center">
                                    <span class="mr-2">共 <?php echo $totalRecords; ?> 条记录</span>
                                    <ul class="pagination pagination-sm m-0 mr-2">
                                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&per_page=<?php echo $perPage; ?>&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>">«</a>
                                        </li>
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>&per_page=<?php echo $perPage; ?>&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&per_page=<?php echo $perPage; ?>&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>">»</a>
                                        </li>
                                    </ul>
                                    <div class="input-group input-group-sm" style="width: 150px;">
                                        <input type="number" class="form-control" id="gotoPage" min="1" placeholder="页码" style="text-align: center;">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" onclick="gotoPage()">
                                                跳转
                                            </button>
                                        </div>
                                    </div>
                                    <script>
                                    function gotoPage() {
                                        const page = document.getElementById('gotoPage').value;
                                        const perPage = <?php echo $perPage; ?>;
                                        if(page > 0 && page <= <?php echo $totalPages; ?>) {
                                            window.location.href = `?page=${page}&per_page=${perPage}&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>`;
                                        }
                                    }
                                    </script>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="float-right d-none d-sm-block">
            <b>Version</b> 1.0.1
        </div>
        <strong>Copyright &copy; 2025 <a href="#">X管家业务系统</a>.</strong> All rights reserved.
    </footer>
</div>
<!-- jQuery -->
<script src="assets/js/jquery-3.7.1.min.js"></script>
<!-- Bootstrap 5.3.0 -->
<script src="assets/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="assets/js/adminlte.min.js"></script>
    <!-- 添加用户模态框 - AdminLTE风格 -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- 模态框头部 -->
                <div class="modal-header bg-primary">
                    <h4 class="modal-title text-white" id="addUserModalLabel">
                        <i class="fas fa-user-plus mr-2"></i>添加新用户
                    </h4>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <!-- 模态框主体 -->
                <div class="modal-body">
                    <!-- 用户信息卡片 -->
                    <div class="card card-outline card-primary">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-info-circle mr-2"></i>用户基本信息
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="addUserForm" method="POST" enctype="multipart/form-data">
                                <input type="hidden" name="action" value="add_user">

                                <!-- 头像上传区域 -->
                                <div class="row mb-4">
                                    <div class="col-12 text-center">
                                        <div class="form-group">
                                            <label class="form-label font-weight-bold">用户头像</label>
                                            <div class="d-flex justify-content-center">
                                                <div class="avatar-upload-container" style="position: relative; cursor: pointer;">
                                                    <img id="avatarPreview" src="images/avatars/gly_default_avatar.jpg"
                                                         class="img-circle elevation-2"
                                                         alt="头像预览"
                                                         style="width: 100px; height: 100px; object-fit: cover; border: 3px solid #007bff;">
                                                    <div class="avatar-overlay" style="position: absolute; top: 0; left: 0; width: 100px; height: 100px; background: rgba(0,0,0,0.5); border-radius: 50%; display: none; align-items: center; justify-content: center;">
                                                        <i class="fas fa-camera text-white" style="font-size: 24px;"></i>
                                                    </div>
                                                    <input type="file" id="avatarInput" class="d-none" name="avatar"
                                                           accept="image/*" onchange="previewAvatar(this)">
                                                </div>
                                            </div>
                                            <small class="form-text text-muted mt-2">点击头像上传图片，支持 JPG、PNG 格式</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- 基本信息表单 -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label font-weight-bold">用户名 <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                </div>
                                                <input type="text" class="form-control" name="username" required placeholder="请输入用户名">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label font-weight-bold">密码 <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                                </div>
                                                <input type="password" class="form-control" name="password" required placeholder="请输入密码">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label font-weight-bold">姓名</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                                </div>
                                                <input type="text" class="form-control" name="name" placeholder="请输入真实姓名">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label font-weight-bold">邮箱</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                                </div>
                                                <input type="email" class="form-control" name="email" placeholder="请输入邮箱地址">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label font-weight-bold">电话</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                                </div>
                                                <input type="tel" class="form-control" name="phone" placeholder="请输入联系电话">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label font-weight-bold">角色 <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-user-tag"></i></span>
                                                </div>
                                                <select class="form-control" name="role" required>
                                                    <option value="">请选择角色</option>
                                                    <option value="admin">管理员</option>
                                                    <option value="salesman">业务员</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label font-weight-bold">状态</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-toggle-on"></i></span>
                                                </div>
                                                <select class="form-control" name="status">
                                                    <option value="active">正常</option>
                                                    <option value="inactive">未激活</option>
                                                    <option value="disabled">已禁用</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 备注信息单独一行 -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label class="form-label font-weight-bold">备注信息</label>
                                            <textarea class="form-control" name="notes" rows="3" placeholder="请输入备注信息（可选）"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 模态框底部 -->
                <div class="modal-footer justify-content-between">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        <i class="fas fa-times mr-2"></i>取消
                    </button>
                    <button type="submit" form="addUserForm" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i>保存用户
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- 模态框相关脚本 -->
    <script>
        // 头像预览功能
        function previewAvatar(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('avatarPreview').src = e.target.result;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 头像点击事件
            $('#avatarPreview').on('click', function() {
                $('#avatarInput').click();
            });

            // 头像悬停效果
            $('.avatar-upload-container').hover(
                function() {
                    $(this).find('.avatar-overlay').show();
                },
                function() {
                    $(this).find('.avatar-overlay').hide();
                }
            );

            // 表单提交验证
            $('#addUserForm').on('submit', function(e) {
                const username = $(this).find('[name="username"]').val();
                const password = $(this).find('[name="password"]').val();
                const role = $(this).find('[name="role"]').val();

                if (!username || !password || !role) {
                    e.preventDefault();
                    alert('用户名、密码和角色不能为空！');
                    return false;
                }
            });

            // 模态框关闭时重置表单
            $('#addUserModal').on('hidden.bs.modal', function () {
                $('#addUserForm')[0].reset();
                $('#avatarPreview').attr('src', 'images/avatars/gly_default_avatar.jpg');
            });
        });
    </script>
    
</style>

<!-- 页面脚本 -->
<script>
function changePerPage(value) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('per_page', value);
    urlParams.set('page', 1);
    window.location.href = '?' + urlParams.toString();
}

// 头像预览功能
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('avatarPreview').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// 表单提交前验证
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    const username = this.querySelector('[name="username"]').value;
    const password = this.querySelector('[name="password"]').value;
    
    if (!username || !password) {
        e.preventDefault();
        alert('用户名和密码不能为空！');
        return false;
    }
});
</script>
    
</body>
</html>

<style>
.avatar-img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
}
.status-active { color: #28a745; }
.status-inactive { color: #ffc107; }
.status-disabled { color: #dc3545; }
