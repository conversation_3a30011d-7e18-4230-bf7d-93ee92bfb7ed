<?php
session_start();
require_once '../includes/auth.php';
require_once '../includes/config.php';
require_once '../includes/functions.php';

// 确保管理员已登录
requireAdminLogin();

// 获取提成记录ID
$rebate_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

try {
    // 获取提成记录信息
    $stmt = $pdo->prepare("
        SELECT r.*, s.name AS salesman_name, s.phone AS salesman_phone, 
               a.netbar_account, a.netbar_name 
        FROM x_rebates r
        JOIN x_salesmen s ON r.salesman_id = s.salesman_id
        JOIN x_accounts a ON r.account_id = a.account_id
        WHERE r.rebate_id = ?
    ");
    $stmt->execute([$rebate_id]);
    $commission = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$commission) {
        throw new Exception('提成记录不存在');
    }

    // 处理表单提交
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // 获取表单数据
        $amount = $_POST['amount'];
        $rebate_month = $_POST['rebate_month'] . '-01';
        $is_withdrawn = $_POST['is_withdrawn'] ?? 0;
        $withdrawal_allowed_date = $_POST['withdrawal_allowed_date'] ?: null;
        $withdrawal_requested_date = $_POST['withdrawal_requested_date'] ?: null;
        $payment_method = $_POST['payment_method'];
        $payment_date = $_POST['payment_date'] ?: null;
        $notes = $_POST['notes'];
        $user_id = $_SESSION['user_id'];

        // 验证必要字段
        if (empty($amount) || empty($rebate_month)) {
            throw new Exception('金额和提成月份为必填项');
        }

        // 更新数据库
        $sql = "UPDATE x_rebates SET
                amount = ?,
                rebate_month = ?,
                is_withdrawn = ?,
                withdrawal_allowed_date = ?,
                withdrawal_requested_date = ?,
                payment_method = ?,
                payment_date = ?,
                notes = ?,
                last_updated_user_id = ?
                WHERE rebate_id = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $amount,
            $rebate_month,
            $is_withdrawn,
            $withdrawal_allowed_date,
            $withdrawal_requested_date,
            $payment_method,
            $payment_date,
            $notes,
            $user_id,
            $rebate_id
        ]);

        // 跳转回管理页面
        header('Location: manage_commissions.php?success=1');
        exit;
    }

    // 获取业务员和账户列表
    $salesmen = $pdo->query("SELECT * FROM x_salesmen")->fetchAll();
    $accounts = $pdo->query("SELECT * FROM x_accounts")->fetchAll();

} catch (Exception $e) {
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X管家业务系统 | 编辑提成记录</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="assets/css/source-sans-pro.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/css/font-awesome.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/css/adminlte.min.css">

</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button">
                    <i class="fas fa-bars"></i>
                </a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="manage_commissions.php" class="nav-link">提成管理</a>
            </li>
        </ul>
    </nav>

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
        <!-- Brand Logo -->
        <a href="index.php" class="brand-link text-center" style="text-decoration: none;">
            <span class="brand-text font-weight-light">X管家业务系统</span>
        </a>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Sidebar Menu -->
            <nav class="mt-2">
                <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu">
                    <li class="nav-item"><a href="index.php" class="nav-link"><i class="nav-icon fas fa-tachometer-alt"></i><p>管理后台首页</p></a></li>
                    <li class="nav-item"><a href="manage_users.php" class="nav-link"><i class="nav-icon fas fa-user-tie"></i><p>后台用户管理</p></a></li>
                    <li class="nav-header">NETBAR</li>
                    <li class="nav-item"><a href="manage_accounts.php" class="nav-link"><i class="nav-icon fas fa-desktop"></i><p>网吧账户管理</p></a></li>
                    <li class="nav-item"><a href="manage_payments.php" class="nav-link"><i class="nav-icon fas fa-yen-sign"></i><p>网吧缴费记录</p></a></li>
                    <li class="nav-header">SALES</li>
                    <li class="nav-item"><a href="manage_salesmen.php" class="nav-link"><i class="nav-icon fas fa-users"></i><p>业务员管理</p></a></li>
                    <li class="nav-item"><a href="manage_commissions.php" class="nav-link active"><i class="nav-icon fas fa-calculator"></i><p>业务提成管理</p></a></li>
                    <li class="nav-header">SETTINGS</li>
                    <li class="nav-item"><a href="manage_versions.php" class="nav-link"><i class="nav-icon fas fa-code-branch"></i><p>软件版本管理</p></a></li>
                    <li class="nav-item"><a href="manage_extension_packages.php" class="nav-link"><i class="nav-icon fas fa-box"></i><p>功能包管理</p></a></li>
                    <li class="nav-item"><a href="manage_salesman_rebate.php" class="nav-link"><i class="nav-icon fas fa-file-invoice-dollar"></i><p>版本提成管理</p></a></li>
                    <li class="nav-header">EXIT</li>
                    <li class="nav-item"><a href="logout.php" class="nav-link"><i class="nav-icon fas fa-sign-out-alt"></i><p>退出系统</p></a></li>
                </ul>
            </nav>
        </div>
    </aside>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Content Header -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>编辑提成记录</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                            <li class="breadcrumb-item"><a href="manage_commissions.php">提成管理</a></li>
                            <li class="breadcrumb-item active">编辑提成记录</li>
                        </ol>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card card-primary card-outline">
                            <div class="card-header">
                                <h3 class="card-title">编辑提成记录 
                                    <?php if ($commission['is_withdrawn'] == 1): ?>
                                        <span class="badge bg-success ms-2">已提现</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning ms-2">未提现</span>
                                    <?php endif; ?>
                                </h3>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-4">

                                
                                <div class="alert alert-info mb-4">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>记录信息：</strong> 
                                            创建于 <?= date('Y-m-d H:i', strtotime($commission['created_at'])) ?>                                        </div>
                                    </div>
                                </div>


                                <form method="post" id="commissionForm" onsubmit="return confirmSubmit()">
                                    <!-- 基本信息卡片 -->
                                    <div class="card mb-4 shadow-sm">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>基本信息</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row g-3">
                                                <!-- 业务员信息 -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">业务员</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                        <input type="text" class="form-control bg-light" 
                                                            value="[<?= $commission['salesman_id'] ?>] <?= htmlspecialchars($commission['salesman_name']) ?> - <?= htmlspecialchars($commission['salesman_phone']) ?>" 
                                                            disabled>
                                                    </div>
                                                </div>

                                                <!-- 网吧名称 -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">网吧名称</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-building"></i></span>
                                                        <input type="text" class="form-control bg-light" 
                                                            value="<?= htmlspecialchars($commission['netbar_name']) ?>" disabled>
                                                    </div>
                                                </div>

                                                <!-- 缴费记录ID -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">缴费记录ID</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-receipt"></i></span>
                                                        <a href="view_payment.php?id=<?= htmlspecialchars($commission['payment_id']) ?>" class="form-control bg-light text-primary" target="_blank">
                                                            <?= htmlspecialchars($commission['payment_id']) ?>
                                                        </a>
                                                    </div>
                                                </div>

                                                <!-- 提成金额 -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">提成金额</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-yen-sign"></i></span>
                                                        <input type="number" class="form-control" name="amount" 
                                                            value="<?= htmlspecialchars($commission['amount']) ?>" required step="0.01">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 提成与提现信息卡片 -->
                                    <div class="card mb-4 shadow-sm">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>提成与提现信息</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row g-3">
                                                <!-- 提成月份 -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">提成月份</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                                        <input type="month" class="form-control" name="rebate_month" 
                                                            value="<?= date('Y-m', strtotime($commission['rebate_month'])) ?>" required>
                                                    </div>
                                                </div>
                                                <!-- 提现状态 -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">是否已提现</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-check-circle"></i></span>
                                                        <select class="form-select" name="is_withdrawn" required>
                                                            <option value="0" <?= $commission['is_withdrawn'] == 0 ? 'selected' : '' ?>>未提现</option>
                                                            <option value="1" <?= $commission['is_withdrawn'] == 1 ? 'selected' : '' ?>>已提现</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <!-- 允许提现时间 -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">允许提现时间</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-unlock"></i></span>
                                                        <input type="datetime-local" class="form-control" name="withdrawal_allowed_date" 
                                                            value="<?= $commission['withdrawal_allowed_date'] ? date('Y-m-d\TH:i', strtotime($commission['withdrawal_allowed_date'])) : '' ?>">
                                                    </div>
                                                </div>
                                                <!-- 申请提现时间 -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">申请提现时间</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-hand-holding-usd"></i></span>
                                                        <input type="datetime-local" class="form-control" name="withdrawal_requested_date" 
                                                            value="<?= $commission['withdrawal_requested_date'] ? date('Y-m-d\TH:i', strtotime($commission['withdrawal_requested_date'])) : '' ?>">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 支付信息卡片 -->
                                    <div class="card mb-4 shadow-sm">
                                        <div class="card-header bg-info text-white">
                                            <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>支付信息</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row g-3">
                                                <!-- 支付方式 -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">支付方式</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-money-check-alt"></i></span>
                                                        <input type="text" class="form-control" name="payment_method" 
                                                            value="<?= htmlspecialchars($commission['payment_method']) ?>">
                                                    </div>
                                                </div>
                                                <!-- 支付日期 -->
                                                <div class="col-md-6">
                                                    <label class="form-label fw-bold">支付日期</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-calendar-check"></i></span>
                                                        <input type="datetime-local" class="form-control" name="payment_date" 
                                                            value="<?= $commission['payment_date'] ? date('Y-m-d\TH:i', strtotime($commission['payment_date'])) : '' ?>">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 备注信息卡片 -->
                                    <div class="card mb-4 shadow-sm">
                                        <div class="card-header bg-warning text-white">
                                            <h5 class="mb-0"><i class="fas fa-sticky-note me-2"></i>备注信息</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <!-- 备注 -->
                                                <div class="col-12">
                                                    <label class="form-label fw-bold">备注</label>
                                                    <textarea class="form-control" name="notes" rows="3"><?= htmlspecialchars($commission['notes']) ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 按钮组 -->
                                    <div class="d-flex justify-content-between mt-4">
                                        <a href="manage_commissions.php" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-1"></i> 返回列表
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i> 保存修改
                                        </button>
                                                </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

        <!-- 返回顶部按钮 -->
    <a id="back-to-top" href="#" class="btn btn-primary back-to-top" role="button" aria-label="返回顶部">
        <i class="fas fa-chevron-up"></i>
    </a>

    <!-- Footer -->
    <footer class="main-footer">
        <strong>Copyright &copy; 2024 X管家业务系统.</strong>
        All rights reserved.
    </footer>
</div>

<!-- jQuery -->
<script src="assets/js/jquery-3.7.1.min.js"></script>
<!-- Bootstrap 5.3.0 -->
<script src="assets/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="assets/js/adminlte.min.js"></script>

<!-- 自定义脚本 -->
<script>
    // 验证表单
    function validateForm() {
        const amount = document.querySelector('input[name="amount"]').value;
        const rebateMonth = document.querySelector('input[name="rebate_month"]').value;
        
        if (!amount || parseFloat(amount) <= 0) {
            alert('请输入有效的提成金额');
            return false;
        }
        
        if (!rebateMonth) {
            alert('请选择提成月份');
            return false;
        }
        
        return true;
    }
    
    // 确认提交表单
    function confirmSubmit() {
        if (!validateForm()) {
            return false;
        }
        return confirm('确定要保存修改吗？');
    }
    
</script>

</body>
</html>
